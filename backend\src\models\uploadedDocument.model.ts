import { Table, Column, Model, DataType, ForeignKey, BelongsTo } from 'sequelize-typescript';
import { Taxpayer } from './taxpayer.model';

export enum DocumentType {
  W2 = 'W2',
  FORM_1099_INT = '1099-INT',
  FORM_1099_DIV = '1099-DIV',
  SCHEDULE_C = 'SCHEDULE-C',
  OTHER = 'OTHER'
}

export enum ProcessingStatus {
  UPLOADED = 'UPLOADED',
  PROCESSING = 'PROCESSING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
  REVIEWED = 'REVIEWED'
}

@Table({
  tableName: 'uploaded_documents',
  timestamps: true,
})
export class UploadedDocument extends Model {
  @Column({
    type: DataType.UUID,
    primaryKey: true,
    defaultValue: DataType.UUIDV4,
  })
  id!: string;

  @ForeignKey(() => Taxpayer)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  taxpayerId!: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    defaultValue: () => new Date().getFullYear() - 1,
  })
  taxYear!: number;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  originalFileName!: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  storedFileName!: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  filePath!: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  mimeType!: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  fileSize!: number;

  @Column({
    type: DataType.ENUM(...Object.values(DocumentType)),
    allowNull: true, // Will be determined by OCR
  })
  documentType!: DocumentType;

  @Column({
    type: DataType.ENUM(...Object.values(ProcessingStatus)),
    allowNull: false,
    defaultValue: ProcessingStatus.UPLOADED,
  })
  processingStatus!: ProcessingStatus;

  @Column({
    type: DataType.JSONB,
    allowNull: true,
  })
  ocrResults!: any; // Store raw OCR results

  @Column({
    type: DataType.JSONB,
    allowNull: true,
  })
  extractedData!: any; // Store structured extracted data

  @Column({
    type: DataType.DECIMAL(3, 2),
    allowNull: true,
    validate: {
      min: 0,
      max: 1,
    },
  })
  confidenceScore!: number; // Overall confidence score (0-1)

  @Column({
    type: DataType.JSONB,
    allowNull: true,
  })
  fieldConfidenceScores!: any; // Individual field confidence scores

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  processingError!: string;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
    defaultValue: false,
  })
  isReviewed!: boolean; // Has user reviewed and confirmed the data

  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
    defaultValue: false,
  })
  isDataUsed!: boolean; // Has the extracted data been used to populate forms

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  relatedFormId!: string; // ID of the form created from this document

  @BelongsTo(() => Taxpayer)
  taxpayer!: Taxpayer;
}

export default UploadedDocument;
