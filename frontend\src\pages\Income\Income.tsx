import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  Box,
  Container,
  Paper,
  TextField,
  Button,
  Grid,
  Divider,
  Alert,
  Snackbar,
  Card,
  CardContent,
  CardActions
} from '@mui/material';
import { useParams, useNavigate } from 'react-router-dom';
import { useF<PERSON>, Controller, useFieldArray } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
// Use simple button text instead of icons to avoid compatibility issues
// We'll use text labels for the buttons instead of icons
import Layout from '../../components/Layout';
import { W2Service } from '../../services';
import { W2 } from '../../types';

// Define validation schema for W-2 form
const w2Schema = z.object({
  employerName: z.string().min(1, 'Employer name is required'),
  employerEin: z.string().regex(/^\d{2}-\d{7}$/, 'EIN must be in format XX-XXXXXXX'),
  employerStreet: z.string().min(1, 'Street address is required'),
  employerCity: z.string().min(1, 'City is required'),
  employerState: z.string().min(1, 'State is required'),
  employerZipCode: z.string().regex(/^\d{5}(-\d{4})?$/, 'ZIP code must be in format XXXXX or XXXXX-XXXX'),
  wages: z.string().min(1, 'Wages are required').transform(val => parseFloat(val)),
  federalIncomeTaxWithheld: z.string().min(1, 'Federal income tax withheld is required').transform(val => parseFloat(val)),
  socialSecurityWages: z.string().min(1, 'Social security wages are required').transform(val => parseFloat(val)),
  socialSecurityTaxWithheld: z.string().min(1, 'Social security tax withheld is required').transform(val => parseFloat(val)),
  medicareWages: z.string().min(1, 'Medicare wages are required').transform(val => parseFloat(val)),
  medicareTaxWithheld: z.string().min(1, 'Medicare tax withheld is required').transform(val => parseFloat(val)),
  stateInfo: z.array(z.object({
    state: z.string().min(1, 'State is required'),
    stateId: z.string().min(1, 'State ID is required'),
    stateWages: z.string().min(1, 'State wages are required').transform(val => parseFloat(val)),
    stateIncomeTaxWithheld: z.string().min(1, 'State income tax withheld is required').transform(val => parseFloat(val)),
  })).optional().default([]),
});

type W2FormData = z.infer<typeof w2Schema>;

const Income: React.FC = () => {
  const { taxYear } = useParams<{ taxYear: string }>();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [w2s, setW2s] = useState<W2[]>([]);
  const [editingW2Id, setEditingW2Id] = useState<string | null>(null);

  const {
    control,
    handleSubmit,
    reset,
    formState: { errors }
  } = useForm<W2FormData>({
    resolver: zodResolver(w2Schema),
    defaultValues: {
      employerName: '',
      employerEin: '',
      employerStreet: '',
      employerCity: '',
      employerState: '',
      employerZipCode: '',
      wages: '',
      federalIncomeTaxWithheld: '',
      socialSecurityWages: '',
      socialSecurityTaxWithheld: '',
      medicareWages: '',
      medicareTaxWithheld: '',
      stateInfo: [{
        state: '',
        stateId: '',
        stateWages: '',
        stateIncomeTaxWithheld: ''
      }],
    },
  });

  const { fields, append, remove } = useFieldArray({
    control,
    name: 'stateInfo',
  });

  // Fetch existing W-2s
  useEffect(() => {
    const fetchW2s = async () => {
      try {
        if (!taxYear) return;

        setLoading(true);
        const data = await W2Service.getW2s(parseInt(taxYear));
        setW2s(data);
      } catch (err) {
        console.error('Error fetching W-2s:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchW2s();
  }, [taxYear]);

  // Load W-2 data for editing
  const handleEditW2 = (w2: W2) => {
    setEditingW2Id(w2.id.toString());

    console.log('Loading W2 for editing:', w2);

    // Format the data for the form
    const stateInfoData = (w2.stateInfos || w2.stateInfo || []).map(info => ({
      state: info.state,
      stateId: info.stateId,
      stateWages: info.stateWages.toString(),
      stateIncomeTaxWithheld: info.stateIncomeTaxWithheld.toString(),
    }));

    // If no state info, add an empty one
    if (stateInfoData.length === 0) {
      stateInfoData.push({
        state: '',
        stateId: '',
        stateWages: '',
        stateIncomeTaxWithheld: ''
      });
    }

    const formData = {
      employerName: w2.employerName || (w2.employerInfo?.name || ''),
      employerEin: w2.employerEin || (w2.employerInfo?.ein || ''),
      employerStreet: w2.employerStreet || (w2.employerInfo?.address?.street || ''),
      employerCity: w2.employerCity || (w2.employerInfo?.address?.city || ''),
      employerState: w2.employerState || (w2.employerInfo?.address?.state || ''),
      employerZipCode: w2.employerZipCode || (w2.employerInfo?.address?.zipCode || ''),
      wages: w2.wages.toString(),
      federalIncomeTaxWithheld: w2.federalIncomeTaxWithheld.toString(),
      socialSecurityWages: w2.socialSecurityWages.toString(),
      socialSecurityTaxWithheld: w2.socialSecurityTaxWithheld.toString(),
      medicareWages: w2.medicareWages.toString(),
      medicareTaxWithheld: w2.medicareTaxWithheld.toString(),
      stateInfo: stateInfoData,
    };

    console.log('Form data for editing:', formData);
    reset(formData);
  };

  // Delete a W-2
  const handleDeleteW2 = async (w2Id: string) => {
    try {
      await W2Service.deleteW2(w2Id);
      setW2s(w2s.filter(w2 => w2.id.toString() !== w2Id));
      setSuccess(true);
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to delete W-2');
    }
  };

  // Cancel editing
  const handleCancelEdit = () => {
    setEditingW2Id(null);
    reset();
  };

  // Submit the form
  const onSubmit = async (data: W2FormData) => {
    try {
      setSubmitting(true);
      setError(null);

      if (!taxYear) {
        throw new Error('Tax year is required');
      }

      // Format the data for the API
      const w2Data = {
        taxYear: parseInt(taxYear),
        employerName: data.employerName,
        employerEin: data.employerEin,
        employerStreet: data.employerStreet,
        employerCity: data.employerCity,
        employerState: data.employerState,
        employerZipCode: data.employerZipCode,
        wages: data.wages,
        federalIncomeTaxWithheld: data.federalIncomeTaxWithheld,
        socialSecurityWages: data.socialSecurityWages || data.wages,
        socialSecurityTaxWithheld: data.socialSecurityTaxWithheld,
        medicareWages: data.medicareWages || data.wages,
        medicareTaxWithheld: data.medicareTaxWithheld,
        stateInfo: data.stateInfo || [],
      };

      let response;

      if (editingW2Id) {
        // Update existing W-2
        response = await W2Service.updateW2(editingW2Id, w2Data);
        setW2s(w2s.map(w2 => w2.id.toString() === editingW2Id ? response.w2 : w2));
      } else {
        // Add new W-2
        response = await W2Service.addW2(w2Data);
        setW2s([...w2s, response.w2]);
      }

      setSuccess(true);
      setEditingW2Id(null);
      reset();
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to save W-2 information');
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <Layout>
      <Container maxWidth="md">
        <Paper sx={{ p: 3, mb: 4 }}>
          <Typography variant="h4" component="h1" gutterBottom>
            Income Information
          </Typography>
          <Typography variant="body1" paragraph>
            Please enter your W-2 information for tax year {taxYear}.
          </Typography>

          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}

          {/* List of existing W-2s */}
          {w2s.length > 0 && (
            <Box sx={{ mb: 4 }}>
              <Typography variant="h6" component="h2" gutterBottom>
                Your W-2 Forms
              </Typography>

              <Grid container spacing={2}>
                {w2s.map((w2) => (
                  <Grid item xs={12} key={w2.id}>
                    <Card>
                      <CardContent>
                        <Typography variant="h6" component="h3">
                          {w2.employerName || (w2.employerInfo?.name || '')}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          EIN: {w2.employerEin || (w2.employerInfo?.ein || '')}
                        </Typography>
                        <Grid container spacing={2} sx={{ mt: 1 }}>
                          <Grid item xs={6}>
                            <Typography variant="body2">
                              <strong>Wages:</strong> ${w2.wages.toFixed(2)}
                            </Typography>
                          </Grid>
                          <Grid item xs={6}>
                            <Typography variant="body2">
                              <strong>Federal Tax Withheld:</strong> ${w2.federalIncomeTaxWithheld.toFixed(2)}
                            </Typography>
                          </Grid>
                        </Grid>
                      </CardContent>
                      <CardActions>
                        <Button
                          size="small"
                          onClick={() => handleEditW2(w2)}
                          disabled={!!editingW2Id}
                        >
                          Edit
                        </Button>
                        <Button
                          size="small"
                          color="error"
                          onClick={() => handleDeleteW2(w2.id.toString())}
                          disabled={!!editingW2Id}
                        >
                          Delete
                        </Button>
                      </CardActions>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            </Box>
          )}

          {/* W-2 Form */}
          <Box sx={{ mb: 2 }}>
            <Typography variant="h6" component="h2" gutterBottom>
              {editingW2Id ? 'Edit W-2 Form' : 'Add W-2 Form'}
            </Typography>

            <Box component="form" onSubmit={handleSubmit(onSubmit)} noValidate>
              <Typography variant="subtitle1" component="h3" gutterBottom sx={{ mt: 2 }}>
                Employer Information
              </Typography>

              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <Controller
                    name="employerName"
                    control={control}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        required
                        fullWidth
                        label="Employer Name"
                        error={!!errors.employerName}
                        helperText={errors.employerName?.message}
                      />
                    )}
                  />
                </Grid>

                <Grid item xs={12} sm={6}>
                  <Controller
                    name="employerEin"
                    control={control}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        required
                        fullWidth
                        label="Employer EIN"
                        placeholder="XX-XXXXXXX"
                        error={!!errors.employerEin}
                        helperText={errors.employerEin?.message || 'Format: XX-XXXXXXX'}
                      />
                    )}
                  />
                </Grid>

                <Grid item xs={12}>
                  <Controller
                    name="employerStreet"
                    control={control}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        required
                        fullWidth
                        label="Street Address"
                        error={!!errors.employerStreet}
                        helperText={errors.employerStreet?.message}
                      />
                    )}
                  />
                </Grid>

                <Grid item xs={12} sm={6}>
                  <Controller
                    name="employerCity"
                    control={control}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        required
                        fullWidth
                        label="City"
                        error={!!errors.employerCity}
                        helperText={errors.employerCity?.message}
                      />
                    )}
                  />
                </Grid>

                <Grid item xs={12} sm={3}>
                  <Controller
                    name="employerState"
                    control={control}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        required
                        fullWidth
                        label="State"
                        error={!!errors.employerState}
                        helperText={errors.employerState?.message}
                      />
                    )}
                  />
                </Grid>

                <Grid item xs={12} sm={3}>
                  <Controller
                    name="employerZipCode"
                    control={control}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        required
                        fullWidth
                        label="ZIP Code"
                        error={!!errors.employerZipCode}
                        helperText={errors.employerZipCode?.message}
                      />
                    )}
                  />
                </Grid>
              </Grid>

              <Typography variant="subtitle1" component="h3" gutterBottom sx={{ mt: 3 }}>
                Income Information
              </Typography>

              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <Controller
                    name="wages"
                    control={control}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        required
                        fullWidth
                        label="Wages (Box 1)"
                        type="number"
                        InputProps={{ inputProps: { min: 0, step: 0.01 } }}
                        error={!!errors.wages}
                        helperText={errors.wages?.message}
                      />
                    )}
                  />
                </Grid>

                <Grid item xs={12} sm={6}>
                  <Controller
                    name="federalIncomeTaxWithheld"
                    control={control}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        required
                        fullWidth
                        label="Federal Income Tax Withheld (Box 2)"
                        type="number"
                        InputProps={{ inputProps: { min: 0, step: 0.01 } }}
                        error={!!errors.federalIncomeTaxWithheld}
                        helperText={errors.federalIncomeTaxWithheld?.message}
                      />
                    )}
                  />
                </Grid>

                <Grid item xs={12} sm={6}>
                  <Controller
                    name="socialSecurityWages"
                    control={control}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        required
                        fullWidth
                        label="Social Security Wages (Box 3)"
                        type="number"
                        InputProps={{ inputProps: { min: 0, step: 0.01 } }}
                        error={!!errors.socialSecurityWages}
                        helperText={errors.socialSecurityWages?.message}
                      />
                    )}
                  />
                </Grid>

                <Grid item xs={12} sm={6}>
                  <Controller
                    name="socialSecurityTaxWithheld"
                    control={control}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        required
                        fullWidth
                        label="Social Security Tax Withheld (Box 4)"
                        type="number"
                        InputProps={{ inputProps: { min: 0, step: 0.01 } }}
                        error={!!errors.socialSecurityTaxWithheld}
                        helperText={errors.socialSecurityTaxWithheld?.message}
                      />
                    )}
                  />
                </Grid>

                <Grid item xs={12} sm={6}>
                  <Controller
                    name="medicareWages"
                    control={control}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        required
                        fullWidth
                        label="Medicare Wages (Box 5)"
                        type="number"
                        InputProps={{ inputProps: { min: 0, step: 0.01 } }}
                        error={!!errors.medicareWages}
                        helperText={errors.medicareWages?.message}
                      />
                    )}
                  />
                </Grid>

                <Grid item xs={12} sm={6}>
                  <Controller
                    name="medicareTaxWithheld"
                    control={control}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        required
                        fullWidth
                        label="Medicare Tax Withheld (Box 6)"
                        type="number"
                        InputProps={{ inputProps: { min: 0, step: 0.01 } }}
                        error={!!errors.medicareTaxWithheld}
                        helperText={errors.medicareTaxWithheld?.message}
                      />
                    )}
                  />
                </Grid>
              </Grid>

              <Typography variant="subtitle1" component="h3" gutterBottom sx={{ mt: 3, mb: 2 }}>
                State Information
                <Button
                  onClick={() => append({ state: '', stateId: '', stateWages: '', stateIncomeTaxWithheld: '' })}
                  sx={{ ml: 2 }}
                  size="small"
                >
                  + Add State
                </Button>
              </Typography>

              {fields.map((field, index) => (
                <Box key={field.id} sx={{ mb: 2 }}>
                  <Grid container spacing={2} alignItems="center">
                    <Grid item xs={12} sm={3}>
                      <Controller
                        name={`stateInfo.${index}.state`}
                        control={control}
                        render={({ field }) => (
                          <TextField
                            {...field}
                            fullWidth
                            label="State"
                            error={!!errors.stateInfo?.[index]?.state}
                            helperText={errors.stateInfo?.[index]?.state?.message}
                          />
                        )}
                      />
                    </Grid>

                    <Grid item xs={12} sm={3}>
                      <Controller
                        name={`stateInfo.${index}.stateId`}
                        control={control}
                        render={({ field }) => (
                          <TextField
                            {...field}
                            fullWidth
                            label="State ID"
                            error={!!errors.stateInfo?.[index]?.stateId}
                            helperText={errors.stateInfo?.[index]?.stateId?.message}
                          />
                        )}
                      />
                    </Grid>

                    <Grid item xs={12} sm={2}>
                      <Controller
                        name={`stateInfo.${index}.stateWages`}
                        control={control}
                        render={({ field }) => (
                          <TextField
                            {...field}
                            fullWidth
                            label="State Wages"
                            type="number"
                            InputProps={{ inputProps: { min: 0, step: 0.01 } }}
                            error={!!errors.stateInfo?.[index]?.stateWages}
                            helperText={errors.stateInfo?.[index]?.stateWages?.message}
                          />
                        )}
                      />
                    </Grid>

                    <Grid item xs={12} sm={3}>
                      <Controller
                        name={`stateInfo.${index}.stateIncomeTaxWithheld`}
                        control={control}
                        render={({ field }) => (
                          <TextField
                            {...field}
                            fullWidth
                            label="State Tax Withheld"
                            type="number"
                            InputProps={{ inputProps: { min: 0, step: 0.01 } }}
                            error={!!errors.stateInfo?.[index]?.stateIncomeTaxWithheld}
                            helperText={errors.stateInfo?.[index]?.stateIncomeTaxWithheld?.message}
                          />
                        )}
                      />
                    </Grid>

                    <Grid item xs={12} sm={1}>
                      <Button
                        onClick={() => remove(index)}
                        color="error"
                        disabled={fields.length === 1}
                        size="small"
                      >
                        Remove
                      </Button>
                    </Grid>
                  </Grid>

                  {index < fields.length - 1 && (
                    <Divider sx={{ my: 2 }} />
                  )}
                </Box>
              ))}

              <Box sx={{ mt: 3, display: 'flex', justifyContent: 'space-between' }}>
                <Button
                  variant="outlined"
                  onClick={() => editingW2Id ? handleCancelEdit() : navigate('/dashboard')}
                >
                  {editingW2Id ? 'Cancel' : 'Back to Dashboard'}
                </Button>

                <Button
                  type="submit"
                  variant="contained"
                  disabled={submitting}
                >
                  {submitting ? 'Saving...' : (editingW2Id ? 'Update W-2' : 'Add W-2')}
                </Button>
              </Box>
            </Box>
          </Box>

          <Box sx={{ mt: 4, display: 'flex', justifyContent: 'space-between' }}>
            <Button
              variant="outlined"
              onClick={() => navigate(`/tax-return/${taxYear}/personal-info`)}
            >
              Previous: Personal Info
            </Button>

            <Button
              variant="contained"
              onClick={() => navigate(`/tax-return/${taxYear}/review`)}
              disabled={w2s.length === 0}
            >
              Next: Review & Calculate
            </Button>
          </Box>
        </Paper>
      </Container>

      <Snackbar
        open={success}
        autoHideDuration={3000}
        onClose={() => setSuccess(false)}
        message={editingW2Id ? "W-2 updated successfully" : "W-2 added successfully"}
      />
    </Layout>
  );
};

export default Income;
