import express, { Request, Response, RequestHandler } from 'express';
import {
  uploadDocument,
  getDocumentStatus,
  getExtractedData,
  markDocumentReviewed,
  getDocuments,
  deleteDocument
} from '../controllers/documentUpload.controller';
import { authenticateJWT } from '../middleware/auth.middleware';
import { upload } from '../services/fileUpload.service';

const router = express.Router();

// All routes require authentication
router.use('/', authenticateJWT);

// Upload a document for OCR processing
router.post('/upload', upload.single('document'), (async (req: Request, res: Response) => {
  await uploadDocument(req, res);
}) as RequestHandler);

// Get processing status of a document
router.get('/:documentId/status', (async (req: Request, res: Response) => {
  await getDocumentStatus(req, res);
}) as RequestHandler);

// Get extracted data from a processed document
router.get('/:documentId/data', (async (req: Request, res: Response) => {
  await getExtractedData(req, res);
}) as RequestHandler);

// Mark document as reviewed by user
router.patch('/:documentId/reviewed', (async (req: Request, res: Response) => {
  await markDocumentReviewed(req, res);
}) as RequestHandler);

// Get all documents for a tax year
router.get('/tax-year/:taxYear', (async (req: Request, res: Response) => {
  await getDocuments(req, res);
}) as RequestHandler);

// Delete a document
router.delete('/:documentId', (async (req: Request, res: Response) => {
  await deleteDocument(req, res);
}) as RequestHandler);

export default router;
