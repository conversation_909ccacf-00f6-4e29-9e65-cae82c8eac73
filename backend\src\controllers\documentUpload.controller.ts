import { Request, Response } from 'express';
import { UploadedDocument, ProcessingStatus } from '../models/uploadedDocument.model';
import { Taxpayer } from '../models/taxpayer.model';
import { validateFile, getFileInfo } from '../services/fileUpload.service';
import documentProcessingService from '../services/documentProcessing.service';

/**
 * Upload a document for OCR processing
 */
export const uploadDocument = async (req: Request, res: Response) => {
  try {
    const userId = req.user.id;
    const { taxYear } = req.body;

    if (!req.file) {
      return res.status(400).json({ message: 'No file uploaded' });
    }

    // Validate file
    const validation = validateFile(req.file);
    if (!validation.isValid) {
      return res.status(400).json({ message: validation.error });
    }

    // Find taxpayer record
    const taxpayer = await Taxpayer.findOne({
      where: { userId: userId, taxYear: parseInt(taxYear) || new Date().getFullYear() - 1 }
    });

    if (!taxpayer) {
      return res.status(404).json({
        message: 'Taxpayer information not found. Please complete your personal information first.'
      });
    }

    // Get file information
    const fileInfo = getFileInfo(req.file);

    // Create document record
    const document = await UploadedDocument.create({
      taxpayerId: taxpayer.id,
      taxYear: taxpayer.taxYear,
      ...fileInfo,
      processingStatus: ProcessingStatus.UPLOADED
    });

    // Start OCR processing asynchronously
    documentProcessingService.processDocument(document.id)
      .catch(error => {
        console.error('OCR processing failed:', error);
      });

    res.status(201).json({
      message: 'Document uploaded successfully',
      document: {
        id: document.id,
        originalFileName: document.originalFileName,
        processingStatus: document.processingStatus,
        createdAt: document.createdAt
      }
    });
  } catch (error) {
    console.error('Upload document error:', error);
    res.status(500).json({ message: 'Server error while uploading document' });
  }
};

/**
 * Get processing status of a document
 */
export const getDocumentStatus = async (req: Request, res: Response) => {
  try {
    const { documentId } = req.params;
    const userId = req.user.id;

    const document = await UploadedDocument.findOne({
      where: { id: documentId },
      include: [{
        model: Taxpayer,
        where: { userId: userId }
      }]
    });

    if (!document) {
      return res.status(404).json({ message: 'Document not found' });
    }

    res.json({
      id: document.id,
      originalFileName: document.originalFileName,
      documentType: document.documentType,
      processingStatus: document.processingStatus,
      confidenceScore: document.confidenceScore,
      isReviewed: document.isReviewed,
      processingError: document.processingError,
      createdAt: document.createdAt,
      updatedAt: document.updatedAt
    });
  } catch (error) {
    console.error('Get document status error:', error);
    res.status(500).json({ message: 'Server error while getting document status' });
  }
};

/**
 * Get extracted data from a processed document
 */
export const getExtractedData = async (req: Request, res: Response) => {
  try {
    const { documentId } = req.params;
    const userId = req.user.id;

    const document = await UploadedDocument.findOne({
      where: { id: documentId },
      include: [{
        model: Taxpayer,
        where: { userId: userId }
      }]
    });

    if (!document) {
      return res.status(404).json({ message: 'Document not found' });
    }

    if (document.processingStatus !== ProcessingStatus.COMPLETED && 
        document.processingStatus !== ProcessingStatus.REVIEWED) {
      return res.status(400).json({ 
        message: 'Document processing not completed',
        status: document.processingStatus
      });
    }

    res.json({
      id: document.id,
      documentType: document.documentType,
      extractedData: document.extractedData,
      fieldConfidenceScores: document.fieldConfidenceScores,
      confidenceScore: document.confidenceScore,
      isReviewed: document.isReviewed
    });
  } catch (error) {
    console.error('Get extracted data error:', error);
    res.status(500).json({ message: 'Server error while getting extracted data' });
  }
};

/**
 * Mark document as reviewed by user
 */
export const markDocumentReviewed = async (req: Request, res: Response) => {
  try {
    const { documentId } = req.params;
    const userId = req.user.id;

    const document = await UploadedDocument.findOne({
      where: { id: documentId },
      include: [{
        model: Taxpayer,
        where: { userId: userId }
      }]
    });

    if (!document) {
      return res.status(404).json({ message: 'Document not found' });
    }

    const updatedDocument = await documentProcessingService.markAsReviewed(documentId);

    res.json({
      message: 'Document marked as reviewed',
      document: {
        id: updatedDocument.id,
        isReviewed: updatedDocument.isReviewed,
        processingStatus: updatedDocument.processingStatus
      }
    });
  } catch (error) {
    console.error('Mark document reviewed error:', error);
    res.status(500).json({ message: 'Server error while marking document as reviewed' });
  }
};

/**
 * Get all documents for a taxpayer
 */
export const getDocuments = async (req: Request, res: Response) => {
  try {
    const userId = req.user.id;
    const { taxYear } = req.params;

    const taxpayer = await Taxpayer.findOne({
      where: { userId: userId, taxYear: parseInt(taxYear) }
    });

    if (!taxpayer) {
      return res.status(404).json({
        message: 'Taxpayer information not found for the specified tax year'
      });
    }

    const documents = await documentProcessingService.getDocuments(
      taxpayer.id,
      parseInt(taxYear)
    );

    const documentsResponse = documents.map(doc => ({
      id: doc.id,
      originalFileName: doc.originalFileName,
      documentType: doc.documentType,
      processingStatus: doc.processingStatus,
      confidenceScore: doc.confidenceScore,
      isReviewed: doc.isReviewed,
      isDataUsed: doc.isDataUsed,
      createdAt: doc.createdAt,
      updatedAt: doc.updatedAt
    }));

    res.json({
      documents: documentsResponse,
      total: documents.length
    });
  } catch (error) {
    console.error('Get documents error:', error);
    res.status(500).json({ message: 'Server error while getting documents' });
  }
};

/**
 * Delete a document
 */
export const deleteDocument = async (req: Request, res: Response) => {
  try {
    const { documentId } = req.params;
    const userId = req.user.id;

    const document = await UploadedDocument.findOne({
      where: { id: documentId },
      include: [{
        model: Taxpayer,
        where: { userId: userId }
      }]
    });

    if (!document) {
      return res.status(404).json({ message: 'Document not found' });
    }

    await documentProcessingService.deleteDocument(documentId);

    res.json({ message: 'Document deleted successfully' });
  } catch (error) {
    console.error('Delete document error:', error);
    res.status(500).json({ message: 'Server error while deleting document' });
  }
};
