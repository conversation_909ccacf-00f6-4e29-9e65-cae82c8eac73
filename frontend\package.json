{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "cypress": "cypress open", "cypress:run": "cypress run", "test:e2e": "cypress run"}, "dependencies": {"@emotion/react": "^11.11.3", "@emotion/styled": "^11.11.0", "@hookform/resolvers": "^3.3.4", "@mui/icons-material": "^5.15.10", "@mui/material": "^5.15.10", "@mui/x-date-pickers": "^6.19.4", "axios": "^1.6.7", "date-fns": "^2.30.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-hook-form": "^7.50.1", "react-router-dom": "^6.22.1", "zod": "^3.22.4"}, "devDependencies": {"@eslint/js": "^8.56.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^14.3.1", "@testing-library/user-event": "^14.6.1", "@types/jest": "^29.5.12", "@types/node": "^20.11.19", "@types/react": "^18.2.57", "@types/react-dom": "^18.2.19", "@vitejs/plugin-react": "^4.2.1", "cypress": "^13.6.4", "dotenv": "^16.5.0", "eslint": "^8.56.0", "eslint-plugin-cypress": "^2.15.1", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "globals": "^13.24.0", "identity-obj-proxy": "^3.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "msw": "^2.8.3", "ts-jest": "^29.1.2", "typescript": "^5.3.3", "typescript-eslint": "^7.0.2", "vite": "^5.1.4"}}